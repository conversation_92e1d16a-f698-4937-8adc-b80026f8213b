import userModel from '../models/user.model.js';

import * as userService from '../services/user.service.js';
import { validationResult } from 'express-validator';

import redisClient from '../services/redis.service.js';

import { getAllUsers } from '../services/user.service.js';
//Controller for createUserController
export const createUserController = async (req, res) => {
    const errors = validationResult(req);

    if(!errors.isEmpty()){
        return res.status(400).json({ errors: errors.array() });
    }

    try {
        const user = await userService.createUser(req.body);

        const token = await user.generateJWT();

        delete user._doc.password;


        res.status(201).json({ user, token });
    } catch (error) {
        res.status(400).send(errors.message);
    }
}


// Controller for loginController
export const loginController = async (req, res) => {
    const errors = validationResult(req);

    if(!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    try {

        const { email, password } = req.body;

        const user = await userModel.findOne({ email }).select('+password');

        if (!user){
           return res.status(401).json({
            
            errors: 'Invalid credentials' 

           })
        }

        const isMatch = await user.isValidPassword(password);

        if(!isMatch){
            return res.status(401).json({ 
                errors: 'Invalid credentials'
            })
        }

        const token = await user.generateJWT();

        delete user._doc.password;

        res.status(200).json({user, token});

    }catch(err) {

        console.log(err);

        res.status(400).send(errors.message);
    }

};


// Controller for profileController
export const profileController = async (req, res) => {
    console.log(req.user);

    res.status(200).json({
        user: req.user
    })
};


//Controller for Logout
export const logoutController = async (req, res) => {
    try{

         let token;

         // Check for token in cookies
         if (req.cookies?.token) {
           token = req.cookies.token;
         }
         // Check for token in Authorization header
         else if (req.headers.authorization?.startsWith("Bearer ")) {
           token = req.headers.authorization.split(" ")[1];
         }
         // Check for token in query parameters (if needed)
         else if (req.query.Authorization?.startsWith("Bearer ")) {
           token = req.query.Authorization.split(" ")[1];
         }

         redisClient.set(token, 'logout', 'EX', 60 * 60 * 24);

         res.status(200).json({
            message: 'Logged out successfully'
         });

    }catch(err){
        console.log(err);
        res.status(400).send(err.message);
    }
};


export const getAllUsersController = async (req, res) => {
    try{

        
        const loggedInUser = await userModel.findOne({
            email: req.user.email
        });


        const allUsers = await userService.getAllUsers({userId: loggedInUser._id});


        return res.status(200).json({
            users: allUsers
        });

    }catch(err){
        console.log(err);
        
        res.status(400).json({ error: err.message });
    }
};