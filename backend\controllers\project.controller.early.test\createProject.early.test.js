
// Unit tests for: createProject


import { validationResult } from 'express-validator';
import userModel from '../../models/user.model.js';
import * as projectService from '../../services/project.service.js';
import { createProject } from '../project.controller';


jest.mock("../../services/project.service.js");
jest.mock("../../models/user.model.js");
jest.mock("express-validator");

describe('createProject() createProject method', () => {
    let req, res, next;

    beforeEach(() => {
        req = {
            body: { name: 'New Project' },
            user: { email: '<EMAIL>' }
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
            send: jest.fn()
        };
        next = jest.fn();
    });

    describe('Happy Paths', () => {
        it('should create a project successfully when inputs are valid', async () => {
            // Arrange
            const mockUser = { _id: 'userId123' };
            const mockProject = { name: 'New Project', userId: 'userId123' };
            validationResult.mockReturnValue({ isEmpty: () => true });
            userModel.findOne.mockResolvedValue(mockUser);
            projectService.createProject.mockResolvedValue(mockProject);

            // Act
            await createProject(req, res, next);

            // Assert
            expect(userModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
            expect(projectService.createProject).toHaveBeenCalledWith({ name: 'New Project', userId: 'userId123' });
            expect(res.status).toHaveBeenCalledWith(201);
            expect(res.json).toHaveBeenCalledWith(mockProject);
        });
    });

    describe('Edge Cases', () => {
        it('should return 400 if validation errors are present', async () => {
            // Arrange
            validationResult.mockReturnValue({ isEmpty: () => false, array: () => [{ msg: 'Invalid input' }] });

            // Act
            await createProject(req, res, next);

            // Assert
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({ errors: [{ msg: 'Invalid input' }] });
        });

        it('should return 400 if user is not found', async () => {
            // Arrange
            validationResult.mockReturnValue({ isEmpty: () => true });
            userModel.findOne.mockResolvedValue(null);

            // Act
            await createProject(req, res, next);

            // Assert
            expect(userModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.send).toHaveBeenCalledWith(expect.any(String));
        });

        it('should handle errors thrown during project creation', async () => {
            // Arrange
            const mockUser = { _id: 'userId123' };
            validationResult.mockReturnValue({ isEmpty: () => true });
            userModel.findOne.mockResolvedValue(mockUser);
            projectService.createProject.mockRejectedValue(new Error('Creation failed'));

            // Act
            await createProject(req, res, next);

            // Assert
            expect(userModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
            expect(projectService.createProject).toHaveBeenCalledWith({ name: 'New Project', userId: 'userId123' });
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.send).toHaveBeenCalledWith('Creation failed');
        });
    });
});

// End of unit tests for: createProject
