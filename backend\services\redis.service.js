// import { Redis } from "ioredis"; // Temporarily disabled for development

// Redis client temporarily disabled for development
// TODO: Re-enable when Redis is properly configured
/*
const redisClient = new Redis({
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true
});

redisClient.on("connect", () => {
    console.log("Redis connected successfully");
});

redisClient.on("error", (err) => {
    console.log("Redis connection error:", err.message);
});

redisClient.on("close", () => {
    console.log("Redis connection closed");
});
*/

// Mock Redis client for development
const redisClient = {
    get: async () => null,
    set: async () => "OK",
    del: async () => 1
};

export default redisClient;