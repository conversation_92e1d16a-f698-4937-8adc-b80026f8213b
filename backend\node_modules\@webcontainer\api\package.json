{"name": "@webcontainer/api", "description": "WebContainer Public API", "version": "1.5.1-internal.6", "author": "StackBlitz Inc.", "license": "MIT", "types": "dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./utils": {"import": {"types": "./dist/utils.d.ts", "default": "./dist/utils.js"}}, "./connect": {"import": {"types": "./dist/connect.d.ts", "default": "./dist/connect.js"}}}, "typesVersions": {"*": {"utils": ["dist/utils.d.ts"], "connect": ["dist/connect.d.ts"]}}, "main": "dist/index.js", "scripts": {"build": "./scripts/build.sh", "prepack": "./scripts/prepack.sh"}, "files": ["dist", "api_reference.json"], "keywords": ["stackblitz", "webcontainer", "nodejs"], "bugs": "https://github.com/stackblitz/webcontainer-core/issues", "homepage": "https://github.com/stackblitz/webcontainer-core", "repository": "stackblitz/webcontainer-core", "devDependencies": {"comlink": "4.3.0", "esbuild": "^0.19.5", "typedoc": "^0.23.24", "typescript": "4.7.4"}, "type": "module"}