{"id": 0, "name": "@webcontainer/api", "kind": 1, "kindString": "Project", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The WebContainer Public API allows you build custom applications on top of an in-browser Node.js runtime.\n\nIts main entrypoint is the "}, {"kind": "inline-tag", "tag": "@link", "text": "WebContainer", "target": 5}, {"kind": "text", "text": " class."}]}, "originalName": "", "children": [{"id": 376, "name": "PreviewMessageType", "kind": 8, "kindString": "Enumeration", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "This type is in a separate module so that localservice can import it\nwithout bundling all the other webcontainer specific stuff."}]}, "children": [{"id": 379, "name": "Console<PERSON><PERSON>r", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "type": {"type": "literal", "value": "PREVIEW_CONSOLE_ERROR"}}, {"id": 377, "name": "UncaughtException", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "type": {"type": "literal", "value": "PREVIEW_UNCAUGHT_EXCEPTION"}}, {"id": 378, "name": "UnhandledRejection", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "type": {"type": "literal", "value": "PREVIEW_UNHANDLED_REJECTION"}}], "groups": [{"title": "Enumeration Members", "children": [379, 377, 378]}]}, {"id": 5, "name": "WebContainer", "kind": 128, "kindString": "Class", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The main export of this library. An instance of "}, {"kind": "code", "text": "`WebContainer`"}, {"kind": "text", "text": " represents a runtime\nready to be used."}]}, "children": [{"id": 20, "name": "_tornDown", "kind": 1024, "kindString": "Property", "flags": {"isPrivate": true}, "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 18, "name": "fs", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Gives access to the underlying file system."}]}, "type": {"type": "reference", "id": 161, "name": "FileSystemAPI"}}, {"id": 19, "name": "internal", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 76, "name": "WebContainerInternal"}}, {"id": 6, "name": "_instance", "kind": 1024, "kindString": "Property", "flags": {"isPrivate": true, "isStatic": true}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 5, "name": "WebContainer"}]}, "defaultValue": "null"}, {"id": 70, "name": "path", "kind": 262144, "kindString": "Accessor", "flags": {}, "getSignature": {"id": 71, "name": "path", "kind": 524288, "kindString": "Get signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The default value of the "}, {"kind": "code", "text": "`PATH`"}, {"kind": "text", "text": " environment variable for processes started through "}, {"kind": "inline-tag", "tag": "@link", "text": "spawn", "target": 28}, {"kind": "text", "text": "."}]}, "type": {"type": "intrinsic", "name": "string"}}}, {"id": 72, "name": "workdir", "kind": 262144, "kindString": "Accessor", "flags": {}, "getSignature": {"id": 73, "name": "workdir", "kind": 524288, "kindString": "Get signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The full path to the working directory (see "}, {"kind": "inline-tag", "tag": "@link", "text": "FileSystemAPI", "target": 161}, {"kind": "text", "text": ")."}]}, "type": {"type": "intrinsic", "name": "string"}}}, {"id": 21, "name": "_unsubscribeFromTokenChangedListener", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "signatures": [{"id": 22, "name": "_unsubscribeFromTokenChangedListener", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 36, "name": "export", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 37, "name": "export", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Exports the provided "}, {"kind": "code", "text": "`path`"}, {"kind": "text", "text": " in the format provided."}], "blockTags": [{"tag": "@example", "content": [{"kind": "code", "text": "```\nconst fileTree = await webcontainer.export('dist', { format: 'json' });\n```"}]}]}, "parameters": [{"id": 38, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The path to serialize."}]}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 290, "name": "FileSystemTree"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 39, "name": "export", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 40, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 41, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intersection", "types": [{"type": "reference", "id": 369, "name": "ExportOptions"}, {"type": "reflection", "declaration": {"id": 42, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 43, "name": "format", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "literal", "value": "json"}}], "groups": [{"title": "Properties", "children": [43]}]}}]}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 290, "name": "FileSystemTree"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 44, "name": "export", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 45, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 46, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 369, "name": "ExportOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 60, "name": "mount", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 61, "name": "mount", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Mounts a tree of files into the filesystem. This can be specified as a tree object ("}, {"kind": "inline-tag", "tag": "@link", "text": "FileSystemTree", "target": 290}, {"kind": "text", "text": ")\nor as a binary snapshot generated by ["}, {"kind": "code", "text": "`@webcontainer/snapshot`"}, {"kind": "text", "text": "](https://www.npmjs.com/package/@webcontainer/snapshot)."}]}, "parameters": [{"id": 62, "name": "snapshotOrTree", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A tree of files, or a binary snapshot. Note that binary payloads will be transferred."}]}, "type": {"type": "union", "types": [{"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qualifiedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "typescript"}, {"type": "reference", "id": 290, "name": "FileSystemTree"}, {"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}]}}, {"id": 63, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 64, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 65, "name": "mountPoint", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Specifies a nested path where the tree should be mounted."}]}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [65]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 47, "name": "on", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 48, "name": "on", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Listens for "}, {"kind": "code", "text": "`port`"}, {"kind": "text", "text": " events, which are emitted when a port is opened or closed by some process."}]}, "parameters": [{"id": 49, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "literal", "value": "port"}}, {"id": 50, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 118, "name": "PortListener"}}], "type": {"type": "reference", "id": 343, "name": "Unsubscribe"}}, {"id": 51, "name": "on", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Listens for "}, {"kind": "code", "text": "`server-ready`"}, {"kind": "text", "text": " events, emitted when a running server is listening for incoming\nconnections and ready to answer requests."}]}, "parameters": [{"id": 52, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "literal", "value": "server-ready"}}, {"id": 53, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 124, "name": "ServerReadyListener"}}], "type": {"type": "reference", "id": 343, "name": "Unsubscribe"}}, {"id": 54, "name": "on", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Listens for "}, {"kind": "code", "text": "`preview-message`"}, {"kind": "text", "text": " events, emitted when a preview sends a "}, {"kind": "code", "text": "`PreviewMessage`"}, {"kind": "text", "text": "."}]}, "parameters": [{"id": 55, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "literal", "value": "preview-message"}}, {"id": 56, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 129, "name": "PreviewMessageListener"}}], "type": {"type": "reference", "id": 343, "name": "Unsubscribe"}}, {"id": 57, "name": "on", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Listens for "}, {"kind": "code", "text": "`error`"}, {"kind": "text", "text": " events, emitted when an internal error is triggered."}]}, "parameters": [{"id": 58, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "literal", "value": "error"}}, {"id": 59, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 133, "name": "ErrorListener"}}], "type": {"type": "reference", "id": 343, "name": "Unsubscribe"}}]}, {"id": 66, "name": "setPreviewScript", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 67, "name": "setPreviewScript", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Set a custom script to be injected into all previews. When this function is called, every\nfuture page reload will contain the provided script tag on all HTML responses.\n\nNote:\n\nWhen this function resolves, every preview reloaded _after_ will have the new script.\nExisting preview have to be explicitely reloaded.\n\nTo reload a preview you can use "}, {"kind": "code", "text": "`reloadPreview`"}, {"kind": "text", "text": "."}]}, "parameters": [{"id": 68, "name": "scriptSrc", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Source for the script tag."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 69, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Options to define which type of script this is."}]}, "type": {"type": "reference", "id": 347, "name": "PreviewScriptOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 28, "name": "spawn", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 29, "name": "spawn", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Spawns a process."}], "blockTags": [{"tag": "@example", "content": [{"kind": "code", "text": "```\nconst install = await webcontainer.spawn('npm', ['i']);\n```"}]}]}, "parameters": [{"id": 30, "name": "command", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The program to be executed."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 31, "name": "args", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The command-line arguments for the program."}]}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 32, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 269, "name": "SpawnOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 255, "name": "WebContainerProcess"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 33, "name": "spawn", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Spawns a process without command-line arguments."}], "blockTags": [{"tag": "@example", "content": [{"kind": "code", "text": "```\nconst install = await webcontainer.spawn('yarn');\n```"}]}]}, "parameters": [{"id": 34, "name": "command", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The program to be executed."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 35, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 269, "name": "SpawnOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 255, "name": "WebContainerProcess"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 74, "name": "teardown", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 75, "name": "teardown", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Destroys the WebContainer instance, turning it unusable, and releases its resources. After this,\na new WebContainer instance can be obtained by calling "}, {"kind": "inline-tag", "tag": "@link", "text": "`boot`", "target": 7}, {"kind": "text", "text": ".\n\nAll entities derived from this instance (e.g. processes, the file system, etc.) also become unusable\nafter calling this method."}]}, "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 7, "name": "boot", "kind": 2048, "kindString": "Method", "flags": {"isStatic": true}, "signatures": [{"id": 8, "name": "boot", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Boots a WebContainer. Only a single instance of WebContainer can be booted concurrently\n(see "}, {"kind": "inline-tag", "tag": "@link", "text": "`teardown`", "target": 74}, {"kind": "text", "text": ").\n\nBooting WebContainer is an expensive operation."}]}, "parameters": [{"id": 9, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 114, "name": "BootOptions"}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 5, "name": "WebContainer"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}], "groups": [{"title": "Properties", "children": [20, 18, 19, 6]}, {"title": "Accessors", "children": [70, 72]}, {"title": "Methods", "children": [21, 36, 60, 47, 66, 28, 74, 7]}]}, {"id": 76, "name": "WebContainerInternal", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 77, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "signatures": [{"id": 78, "name": "new WebContainerInternal", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "type": {"type": "reference", "id": 76, "name": "WebContainerInternal"}}]}, {"id": 90, "name": "getProcesses", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 91, "name": "getProcesses", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "array", "elementType": {"type": "reflection", "declaration": {"id": 92, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 93, "name": "pid", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "children": [93]}]}}}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 94, "name": "onProcessesRemove", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 95, "name": "onProcessesRemove", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 96, "name": "cb", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 97, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 98, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 99, "name": "process", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 100, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 101, "name": "pid", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "children": [101]}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reflection", "declaration": {"id": 102, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 103, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "void"}}]}}}]}, {"id": 104, "name": "serialize", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 105, "name": "serialize", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 106, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 107, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 231, "name": "GlobOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 111, "name": "setCORSAuthToken", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 112, "name": "setCORSAuthToken", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 113, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 108, "name": "setCORSProxy", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 109, "name": "setCORSProxy", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 110, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 373, "name": "ProxyOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 81, "name": "watchPaths", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 82, "name": "watchPaths", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 83, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 146, "name": "WatchPathsOptions"}}, {"id": 84, "name": "cb", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 85, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 86, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 87, "name": "events", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "array", "elementType": {"type": "reference", "id": 155, "name": "PathWatcherEvent"}}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reflection", "declaration": {"id": 88, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 89, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "void"}}]}}}]}], "groups": [{"title": "Constructors", "children": [77]}, {"title": "Methods", "children": [90, 94, 104, 111, 108, 81]}]}, {"id": 293, "name": "AuthAPI", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Module to authenticate users. Once authenticated, the WebContainer instance\ncan fetch private packages to which the user has access.\n\nIf you use this module, you should call "}, {"kind": "code", "text": "`auth.init()`"}, {"kind": "text", "text": " once at application initialization time.\nIf you do server side rendering, it should be called on any page that uses the API.\n\nThe AuthAPI uses OAuth 2.0 with PKCE and reads parameters from the URL:\n\n - "}, {"kind": "code", "text": "`code`"}, {"kind": "text", "text": ":  This contains the OAuth code needed to get credentials.\n - "}, {"kind": "code", "text": "`error`"}, {"kind": "text", "text": ": This contains an error message if authentication fails.\n            Typically, if the user declines the authorization."}]}, "children": [{"id": 331, "name": "forget", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 332, "name": "forget", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": []}]}, "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 294, "name": "init", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 295, "name": "init", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Intialize the authentication for use in WebContainer."}]}, "parameters": [{"id": 296, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Options to initialize the authentication of users."}]}, "type": {"type": "reference", "id": 333, "name": "AuthInitOptions"}}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 297, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 298, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": "need-auth"}, {"type": "literal", "value": "authorized"}]}}], "groups": [{"title": "Properties", "children": [298]}]}}, {"type": "reference", "id": 339, "name": "AuthFailedError"}]}}]}, {"id": 304, "name": "loggedIn", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 305, "name": "loggedIn", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Returns a promise that resolves when the user authorized your application.\nThis promise is guaranteed to never be rejected.\n\nIf the user never authorizes or declines your application, this promise never\nresolves.\n\n### Example:\n\n"}, {"kind": "code", "text": "```ts\nconst instance = await WebContainer.boot();\n\n// wait until the user is logged in\nawait auth.loggedIn();\n\n// we can now fetch packages\nawait instance.spawn('npm', ['install']);\n```"}]}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 306, "name": "logout", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 307, "name": "logout", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Logout the user and clear any credentials that were saved locally."}]}, "parameters": [{"id": 308, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "If "}, {"kind": "code", "text": "`ignoreRevokeError`"}, {"kind": "text", "text": " is set and the revocation failed, the locally-saved credentials are discarded nonetheless."}]}, "type": {"type": "reflection", "declaration": {"id": 309, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 310, "name": "ignoreRevokeError", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "children": [310]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 311, "name": "on", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 312, "name": "on", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Listens for 'logged-out' events, which are emitted when the credentials are revoked, meaning the user needs to re-authenticate."}]}, "parameters": [{"id": 313, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "literal", "value": "logged-out"}}, {"id": 314, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 315, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 316, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "id": 343, "name": "Unsubscribe"}}, {"id": 317, "name": "on", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Listens for 'auth-failed' events, which are emitted when the user declines authorization in another tab / popup."}]}, "parameters": [{"id": 318, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "literal", "value": "auth-failed"}}, {"id": 319, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 320, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 321, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 322, "name": "reason", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 323, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 325, "name": "description", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 324, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [325, 324]}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "id": 343, "name": "Unsubscribe"}}]}, {"id": 299, "name": "startAuthFlow", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 300, "name": "startAuthFlow", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "This starts the OAuth flow, redirecting the current page to the\nStackBlitz editor to authenticate the user."}]}, "parameters": [{"id": 301, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "If "}, {"kind": "code", "text": "`options.popup`"}, {"kind": "text", "text": " is set to true, then instead of redirecting the current page, a popup is opened."}]}, "type": {"type": "reflection", "declaration": {"id": 302, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 303, "name": "popup", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "children": [303]}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 326, "name": "tokens", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 327, "name": "tokens", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": []}]}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reflection", "declaration": {"id": 328, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 329, "name": "access", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 330, "name": "refresh", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [329, 330]}]}}]}}]}], "groups": [{"title": "Methods", "children": [331, 294, 304, 306, 311, 299, 326]}]}, {"id": 339, "name": "AuthFailedError", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Authentication error used when authentication fails, likely because the user refused to grant\naccess or because they don't have permission."}]}, "children": [{"id": 342, "name": "description", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A detailed description of the error."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 341, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A short description of the error."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 340, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "literal", "value": "auth-failed"}}], "groups": [{"title": "Properties", "children": [342, 341, 340]}]}, {"id": 333, "name": "AuthInitOptions", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Options provided to "}, {"kind": "code", "text": "`auth.init(...)`"}, {"kind": "text", "text": "."}]}, "children": [{"id": 335, "name": "clientId", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The client id for this OAuth application."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 334, "name": "editor<PERSON><PERSON><PERSON>", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "StackB<PERSON>' origin."}], "blockTags": [{"tag": "@default", "content": [{"kind": "text", "text": "https://stackblitz.com"}]}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 337, "name": "<PERSON><PERSON><PERSON>", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": []}]}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 338, "name": "redirectUri", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": []}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 336, "name": "scope", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "OAuth scope."}], "blockTags": [{"tag": "@see", "content": [{"kind": "text", "text": "https://www.rfc-editor.org/rfc/rfc6749#section-3.3"}]}]}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [335, 334, 337, 338, 336]}]}, {"id": 351, "name": "BasePreviewMessage", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 356, "name": "hash", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 354, "name": "pathname", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 353, "name": "port", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 352, "name": "previewId", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 355, "name": "search", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [356, 354, 353, 352, 355]}]}, {"id": 114, "name": "BootOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 115, "name": "coep", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "The value of the "}, {"kind": "inline-tag", "tag": "@link", "text": "COEP", "target": "https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Embedder-Policy"}, {"kind": "text", "text": " header\nused to load your application.\n\nChoosing 'none' will result in no cross-origin isolation headers being used. This will only work on Chromium-based\nbrowsers as long as an Origin Trial is supported.\n\nThis value is fixed the first time a WebContainer is booted, and cannot be changed in successive reboots.\n\nFor more info about cross-origin isolation, see our "}, {"kind": "inline-tag", "tag": "@link", "text": "docs", "target": "https://webcontainers.io/guides/quickstart"}, {"kind": "text", "text": "."}]}, "type": {"type": "union", "types": [{"type": "literal", "value": "none"}, {"type": "literal", "value": "require-corp"}, {"type": "literal", "value": "credentialless"}]}}, {"id": 117, "name": "forwardPreviewErrors", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Whether errors occurring in preview iframes on the current page should be forwarded\nto the parent page. Captured errors originate from:\n\n - Calls to "}, {"kind": "code", "text": "`console.error`"}, {"kind": "text", "text": "\n - Any "}, {"kind": "code", "text": "`unhandledrejection`"}, {"kind": "text", "text": " events on "}, {"kind": "code", "text": "`window`"}, {"kind": "text", "text": "\n - Any uncaught "}, {"kind": "code", "text": "`error`"}, {"kind": "text", "text": " events on "}, {"kind": "code", "text": "`window`"}, {"kind": "text", "text": "\n\nIf set to 'exceptions-only', 'console.error's are not forwarded."}], "blockTags": [{"tag": "@default", "content": [{"kind": "text", "text": "false"}]}]}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "boolean"}, {"type": "literal", "value": "exceptions-only"}]}}, {"id": 116, "name": "workdirName", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Sets the _folder name_ for the working directory of your WebContainer instance (see "}, {"kind": "inline-tag", "tag": "@link", "text": "FileSystemAPI", "target": 161}, {"kind": "text", "text": ").\nIf not provided, it will be auto-generated.\n\nThis is mostly a \"cosmetic\" option."}]}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [115, 117, 116]}]}, {"id": 357, "name": "ConsoleErrorMessage", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 359, "name": "args", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "any"}}}, {"id": 360, "name": "stack", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 358, "name": "type", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 379, "name": "Console<PERSON><PERSON>r"}}], "groups": [{"title": "Properties", "children": [359, 360, 358]}]}, {"id": 139, "name": "DirEnt", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A representation of a directory entry,\nsee "}, {"kind": "inline-tag", "tag": "@link", "text": "the Node.js API", "target": "https://nodejs.org/dist/latest-v16.x/docs/api/fs.html#class-fsdirent"}, {"kind": "text", "text": "."}]}, "children": [{"id": 140, "name": "name", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 145, "name": "T"}}, {"id": 143, "name": "isDirectory", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 144, "name": "isDirectory", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "boolean"}}]}, {"id": 141, "name": "isFile", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 142, "name": "isFile", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "boolean"}}]}], "groups": [{"title": "Properties", "children": [140]}, {"title": "Methods", "children": [143, 141]}], "typeParameters": [{"id": 145, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}}]}, {"id": 280, "name": "DirectoryNode", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Represents a directory, see "}, {"kind": "inline-tag", "tag": "@link", "text": "FileSystemTree", "target": 290}, {"kind": "text", "text": "."}]}, "children": [{"id": 281, "name": "directory", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 290, "name": "FileSystemTree"}}], "groups": [{"title": "Properties", "children": [281]}]}, {"id": 369, "name": "ExportOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 372, "name": "excludes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Globbing patterns to exclude files from the export."}]}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 370, "name": "format", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "The format of the export."}], "blockTags": [{"tag": "@default", "content": [{"kind": "text", "text": "json"}]}]}, "type": {"type": "union", "types": [{"type": "literal", "value": "json"}, {"type": "literal", "value": "binary"}, {"type": "literal", "value": "zip"}]}}, {"id": 371, "name": "includes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Globbing patterns to include files from within excluded folders."}]}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}], "groups": [{"title": "Properties", "children": [372, 370, 371]}]}, {"id": 282, "name": "FileNode", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Represents a file, see "}, {"kind": "inline-tag", "tag": "@link", "text": "FileSystemTree", "target": 290}, {"kind": "text", "text": "."}]}, "children": [{"id": 283, "name": "file", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 284, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 285, "name": "contents", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The contents of the file, either as a UTF-8 string or as raw binary."}]}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}]}}], "groups": [{"title": "Properties", "children": [285]}]}}}], "groups": [{"title": "Properties", "children": [283]}]}, {"id": 161, "name": "FileSystemAPI", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Interface to interact directly with the WebContainer filesystem. Modeled after\n"}, {"kind": "inline-tag", "tag": "@link", "text": "`fs.promises`", "target": "https://nodejs.org/dist/latest-v16.x/docs/api/fs.html#promises-api"}, {"kind": "text", "text": " in Node.\n\nFile system operations exposed here are scoped to the working directory: a given folder predetermined at boot time.\nAll paths are resolved with respect to this working directory."}]}, "children": [{"id": 201, "name": "mkdir", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 202, "name": "mkdir", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 203, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 204, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 205, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 206, "name": "recursive", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "literal", "value": false}}], "groups": [{"title": "Properties", "children": [206]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 207, "name": "mkdir", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 208, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 209, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 210, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 211, "name": "recursive", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "literal", "value": true}}], "groups": [{"title": "Properties", "children": [211]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 187, "name": "readFile", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 188, "name": "readFile", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 189, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 190, "name": "encoding", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "literal", "value": null}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 191, "name": "readFile", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 192, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 193, "name": "encoding", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 279, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 162, "name": "readdir", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 163, "name": "readdir", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 164, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 165, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": "buffer"}, {"type": "reflection", "declaration": {"id": 166, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 167, "name": "encoding", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "literal", "value": "buffer"}}, {"id": 168, "name": "withFileTypes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "literal", "value": false}}], "groups": [{"title": "Properties", "children": [167, 168]}]}}]}}], "type": {"type": "reference", "typeArguments": [{"type": "array", "elementType": {"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 169, "name": "readdir", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 170, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 171, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reflection", "declaration": {"id": 172, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 173, "name": "encoding", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 279, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}, {"id": 174, "name": "withFileTypes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "literal", "value": false}}], "groups": [{"title": "Properties", "children": [173, 174]}]}}, {"type": "reference", "id": 279, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 175, "name": "readdir", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 176, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 177, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 178, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 179, "name": "encoding", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "literal", "value": "buffer"}}, {"id": 180, "name": "withFileTypes", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "literal", "value": true}}], "groups": [{"title": "Properties", "children": [179, 180]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "array", "elementType": {"type": "reference", "id": 139, "typeArguments": [{"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}], "name": "DirEnt"}}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 181, "name": "readdir", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 182, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 183, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 184, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 185, "name": "encoding", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 279, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}, {"id": 186, "name": "withFileTypes", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "literal", "value": true}}], "groups": [{"title": "Properties", "children": [185, 186]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "array", "elementType": {"type": "reference", "id": 139, "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "DirEnt"}}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 219, "name": "rename", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 220, "name": "rename", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 221, "name": "old<PERSON><PERSON>", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 222, "name": "newPath", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 212, "name": "rm", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 213, "name": "rm", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 214, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 215, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 216, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 217, "name": "force", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 218, "name": "recursive", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "children": [217, 218]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}, {"id": 223, "name": "watch", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 224, "name": "watch", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 225, "name": "filename", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 226, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 245, "name": "FSWatchOptions"}}, {"id": 227, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 250, "name": "FSWatchCallback"}}], "type": {"type": "reference", "id": 242, "name": "IFSWatcher"}}, {"id": 228, "name": "watch", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 229, "name": "filename", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 230, "name": "listener", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 250, "name": "FSWatchCallback"}}], "type": {"type": "reference", "id": 242, "name": "IFSWatcher"}}]}, {"id": 194, "name": "writeFile", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 195, "name": "writeFile", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 196, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 197, "name": "data", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}]}}, {"id": 198, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}, {"type": "reflection", "declaration": {"id": 199, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 200, "name": "encoding", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Properties", "children": [200]}]}}]}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}], "groups": [{"title": "Methods", "children": [201, 187, 162, 219, 212, 223, 194]}]}, {"id": 290, "name": "FileSystemTree", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A simple, tree-like structure to describe the contents of a folder to be mounted."}], "blockTags": [{"tag": "@example", "content": [{"kind": "code", "text": "```\nconst tree = {\n  myproject: {\n    directory: {\n      'foo.js': {\n        file: {\n          contents: 'const x = 1;',\n        },\n      },\n      .envrc: {\n        file: {\n          contents: 'ENVIRONMENT=staging'\n        }\n      },\n    },\n  },\n  emptyFolder: {\n    directory: {}\n  },\n};\n```"}]}]}, "indexSignature": {"id": 291, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 292, "name": "name", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "union", "types": [{"type": "reference", "id": 280, "name": "DirectoryNode"}, {"type": "reference", "id": 282, "name": "FileNode"}, {"type": "reference", "id": 286, "name": "SymlinkNode"}]}}}, {"id": 231, "name": "GlobOptions", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": [{"kind": "text", "text": "Options for serialization."}]}]}, "children": [{"id": 233, "name": "excludes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 240, "name": "external", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 241, "name": "format", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [], "blockTags": [{"tag": "@default", "content": [{"kind": "text", "text": "json"}]}]}, "type": {"type": "union", "types": [{"type": "literal", "value": "json"}, {"type": "literal", "value": "binary"}, {"type": "literal", "value": "zip"}, {"type": "literal", "value": "pretty"}]}}, {"id": 234, "name": "homeDir", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 238, "name": "ignoreSymlinks", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 239, "name": "includeTimestamps", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 232, "name": "includes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 236, "name": "requireGit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 235, "name": "useGitignore", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 237, "name": "useGlobalGitignore", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "children": [233, 240, 241, 234, 238, 239, 232, 236, 235, 237]}]}, {"id": 242, "name": "IFSWatcher", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Interface for manipulating watching."}]}, "children": [{"id": 243, "name": "close", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 244, "name": "close", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "void"}}]}], "groups": [{"title": "Methods", "children": [243]}]}, {"id": 155, "name": "PathWatcherEvent", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": []}]}, "children": [{"id": 159, "name": "buffer", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}}, {"id": 160, "name": "ino", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 158, "name": "mtime", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 157, "name": "path", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 156, "name": "type", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": "change"}, {"type": "literal", "value": "add_file"}, {"type": "literal", "value": "remove_file"}, {"type": "literal", "value": "add_dir"}, {"type": "literal", "value": "remove_dir"}, {"type": "literal", "value": "update_directory"}]}}], "groups": [{"title": "Properties", "children": [159, 160, 158, 157, 156]}]}, {"id": 347, "name": "PreviewScriptOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 350, "name": "async", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 349, "name": "defer", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 348, "name": "type", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "literal", "value": "module"}, {"type": "literal", "value": "importmap"}]}}], "groups": [{"title": "Properties", "children": [350, 349, 348]}]}, {"id": 373, "name": "ProxyOptions", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": []}]}, "children": [{"id": 374, "name": "address", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 375, "name": "domains", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}], "groups": [{"title": "Properties", "children": [374, 375]}]}, {"id": 269, "name": "SpawnOptions", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Options that control process spawning."}]}, "children": [{"id": 270, "name": "cwd", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Current working directory for the process, relative to the "}, {"kind": "inline-tag", "tag": "@link", "text": "`workdir`", "target": 72}, {"kind": "text", "text": " of this instance (which\nyou can change when "}, {"kind": "inline-tag", "tag": "@link", "text": "booting `WebContainer`", "target": 7}, {"kind": "text", "text": ").\n\nBy default, the working directory of the spawned process is "}, {"kind": "inline-tag", "tag": "@link", "text": "`workdir`", "target": 72}, {"kind": "text", "text": "."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 271, "name": "env", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Environment variables to set for the process."}]}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "number"}, {"type": "intrinsic", "name": "boolean"}]}], "name": "Record", "qualifiedName": "Record", "package": "typescript"}}, {"id": 272, "name": "output", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "When set to false, no terminal output is sent back to the process,\nand the "}, {"kind": "inline-tag", "tag": "@link", "text": "`output`", "target": 258}, {"kind": "text", "text": " stream will never produce any chunks."}]}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 278, "name": "stderr", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Like "}, {"kind": "code", "text": "`output`"}, {"kind": "text", "text": ", but for standard error (though the default is "}, {"kind": "code", "text": "`false`"}, {"kind": "text", "text": ")."}]}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 277, "name": "stdout", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Like "}, {"kind": "code", "text": "`output`"}, {"kind": "text", "text": ", but for standard output (though the default is "}, {"kind": "code", "text": "`false`"}, {"kind": "text", "text": ")."}]}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 273, "name": "terminal", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "The size of the attached terminal."}]}, "type": {"type": "reflection", "declaration": {"id": 274, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 275, "name": "cols", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 276, "name": "rows", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "children": [275, 276]}]}}}], "groups": [{"title": "Properties", "children": [270, 271, 272, 278, 277, 273]}]}, {"id": 286, "name": "SymlinkNode", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Represents a symlink, see "}, {"kind": "inline-tag", "tag": "@link", "text": "FileSystemTree", "target": 290}, {"kind": "text", "text": "."}]}, "children": [{"id": 287, "name": "file", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 288, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 289, "name": "symlink", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The target of the symlink."}]}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [289]}]}}}], "groups": [{"title": "Properties", "children": [287]}]}, {"id": 361, "name": "UncaughtExceptionMessage", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 363, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 364, "name": "stack", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 362, "name": "type", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 377, "name": "UncaughtException"}}], "groups": [{"title": "Properties", "children": [363, 364, 362]}]}, {"id": 365, "name": "UnhandledRejectionMessage", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 367, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 368, "name": "stack", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 366, "name": "type", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 378, "name": "UnhandledRejection"}}], "groups": [{"title": "Properties", "children": [367, 368, 366]}]}, {"id": 146, "name": "WatchPathsOptions", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [], "blockTags": [{"tag": "@unstableInternal", "content": []}]}, "children": [{"id": 149, "name": "exclude", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 154, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 150, "name": "gitignore", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 152, "name": "ignoreHiddenFiles", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 153, "name": "ignoreHiddenFolders", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 148, "name": "include", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 151, "name": "<PERSON><PERSON><PERSON><PERSON>", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 147, "name": "path", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [149, 154, 150, 152, 153, 148, 151, 147]}]}, {"id": 255, "name": "WebContainerProcess", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A running process spawned in a "}, {"kind": "inline-tag", "tag": "@link", "text": "WebContainer", "target": 5}, {"kind": "text", "text": "."}]}, "children": [{"id": 256, "name": "exit", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A promise for the exit code of the process."}]}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "number"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}, {"id": 257, "name": "input", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "An input stream for the attached pseudoterminal device."}]}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "WritableStream", "qualifiedName": "WritableStream", "package": "typescript"}}, {"id": 258, "name": "output", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A stream that receives all terminal output, including the stdout and stderr emitted by the spawned process\n_and_ its descendants.\n\nCan be disabled by setting "}, {"kind": "inline-tag", "tag": "@link", "text": "`output`", "target": 269}, {"kind": "text", "text": " to "}, {"kind": "code", "text": "`false`"}, {"kind": "text", "text": "."}]}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "ReadableStream", "qualifiedName": "ReadableStream", "package": "typescript"}}, {"id": 268, "name": "stderr", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "ReadableStream", "qualifiedName": "ReadableStream", "package": "typescript"}}, {"id": 267, "name": "stdout", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "ReadableStream", "qualifiedName": "ReadableStream", "package": "typescript"}}, {"id": 259, "name": "kill", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 260, "name": "kill", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Kills the process."}]}, "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 261, "name": "resize", "kind": 2048, "kindString": "Method", "flags": {}, "signatures": [{"id": 262, "name": "resize", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Resizes the attached terminal."}]}, "parameters": [{"id": 263, "name": "dimensions", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 264, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 265, "name": "cols", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 266, "name": "rows", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "children": [265, 266]}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}], "groups": [{"title": "Properties", "children": [256, 257, 258, 268, 267]}, {"title": "Methods", "children": [259, 261]}]}, {"id": 279, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 4194304, "kindString": "Type alias", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Represents the character encoding options available for encoding and decoding data in Node.js buffers."}], "blockTags": [{"tag": "@see", "content": [{"kind": "inline-tag", "tag": "@link", "text": "Buffer Documentation", "target": "https://nodejs.org/api/buffer.html#buffer_buffers_and_character_encodings|Node.js"}]}]}, "type": {"type": "union", "types": [{"type": "literal", "value": "ascii"}, {"type": "literal", "value": "utf8"}, {"type": "literal", "value": "utf-8"}, {"type": "literal", "value": "utf16le"}, {"type": "literal", "value": "ucs2"}, {"type": "literal", "value": "ucs-2"}, {"type": "literal", "value": "base64"}, {"type": "literal", "value": "base64url"}, {"type": "literal", "value": "latin1"}, {"type": "literal", "value": "binary"}, {"type": "literal", "value": "hex"}]}}, {"id": 133, "name": "ErrorListener", "kind": 4194304, "kindString": "Type alias", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 134, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 135, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 136, "name": "error", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The emitted error."}]}, "type": {"type": "reflection", "declaration": {"id": 137, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 138, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [138]}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}}}, {"id": 250, "name": "FSWatchCallback", "kind": 4194304, "kindString": "Type alias", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 251, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 252, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Callback triggered on watch events."}]}, "parameters": [{"id": 253, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": "rename"}, {"type": "literal", "value": "change"}]}}, {"id": 254, "name": "filename", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "name": "Uint8Array", "qualifiedName": "Uint8Array", "package": "typescript"}]}}], "type": {"type": "intrinsic", "name": "void"}}]}}}, {"id": 245, "name": "FSWatchOptions", "kind": 4194304, "kindString": "Type alias", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Options for configuring fs.watch."}]}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 246, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 247, "name": "encoding", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "reference", "id": 279, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "literal", "value": null}]}}, {"id": 248, "name": "persistent", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 249, "name": "recursive", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "children": [247, 248, 249]}]}}, {"type": "intrinsic", "name": "string"}, {"type": "literal", "value": null}]}}, {"id": 118, "name": "PortListener", "kind": 4194304, "kindString": "Type alias", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 119, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 120, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 121, "name": "port", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The port on which the server is listening."}]}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 122, "name": "type", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The new status of the port."}]}, "type": {"type": "union", "types": [{"type": "literal", "value": "open"}, {"type": "literal", "value": "close"}]}}, {"id": 123, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The url where the server can be accessed."}]}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}, {"id": 346, "name": "PreviewMessage", "kind": 4194304, "kindString": "Type alias", "flags": {}, "type": {"type": "intersection", "types": [{"type": "union", "types": [{"type": "reference", "id": 361, "name": "UncaughtExceptionMessage"}, {"type": "reference", "id": 365, "name": "UnhandledRejectionMessage"}, {"type": "reference", "id": 357, "name": "ConsoleErrorMessage"}]}, {"type": "reference", "id": 351, "name": "BasePreviewMessage"}]}}, {"id": 129, "name": "PreviewMessageListener", "kind": 4194304, "kindString": "Type alias", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 130, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 131, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 132, "name": "message", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The message sent by a preview."}]}, "type": {"type": "reference", "id": 346, "name": "PreviewMessage"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}, {"id": 124, "name": "ServerReadyListener", "kind": 4194304, "kindString": "Type alias", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 125, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 126, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 127, "name": "port", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The port on which the readied server is listening."}]}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 128, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The url where the server can be accessed."}]}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}, {"id": 343, "name": "Unsubscribe", "kind": 4194304, "kindString": "Type alias", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 344, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 345, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "void"}}]}}}, {"id": 4, "name": "auth", "kind": 32, "kindString": "Variable", "flags": {"isConst": true}, "type": {"type": "reference", "id": 293, "name": "AuthAPI"}, "defaultValue": "authImpl"}, {"id": 1, "name": "configureAPIKey", "kind": 64, "kindString": "Function", "flags": {}, "signatures": [{"id": 2, "name": "configureAPIKey", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Configure an API key to be used for this instance of WebContainer."}]}, "parameters": [{"id": 3, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "WebContainer API key."}]}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 384, "name": "isPreviewMessage", "kind": 64, "kindString": "Function", "flags": {}, "signatures": [{"id": 385, "name": "isPreviewMessage", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 386, "name": "data", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "predicate", "name": "data", "asserts": false, "targetType": {"type": "reference", "id": 346, "name": "PreviewMessage"}}}]}, {"id": 387, "name": "nullPrototype", "kind": 64, "kindString": "Function", "flags": {}, "signatures": [{"id": 388, "name": "nullPrototype", "kind": 4096, "kindString": "Call signature", "flags": {}, "typeParameter": [{"id": 389, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "any"}, {"type": "intrinsic", "name": "any"}], "name": "Record", "qualifiedName": "Record", "package": "typescript"}}], "parameters": [{"id": 390, "name": "source", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 389, "name": "T"}}], "type": {"type": "reference", "id": 389, "name": "T"}}]}, {"id": 380, "name": "reloadPreview", "kind": 64, "kindString": "Function", "flags": {}, "signatures": [{"id": 381, "name": "reloadPreview", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "This function reloads the provided iframe."}]}, "parameters": [{"id": 382, "name": "preview", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The iframe page to reload."}]}, "type": {"type": "reference", "name": "HTMLIFrameElement", "qualifiedName": "HTMLIFrameElement", "package": "typescript"}}, {"id": 383, "name": "hardRefreshTimeout", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The timeout after which the preview is reset if it hasn't responded to the reload event."}]}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "200"}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "qualifiedName": "Promise", "package": "typescript"}}]}], "groups": [{"title": "Enumerations", "children": [376]}, {"title": "Classes", "children": [5, 76]}, {"title": "Interfaces", "children": [293, 339, 333, 351, 114, 357, 139, 280, 369, 282, 161, 290, 231, 242, 155, 347, 373, 269, 286, 361, 365, 146, 255]}, {"title": "Type Aliases", "children": [279, 133, 250, 245, 118, 346, 129, 124, 343]}, {"title": "Variables", "children": [4]}, {"title": "Functions", "children": [1, 384, 387, 380]}]}