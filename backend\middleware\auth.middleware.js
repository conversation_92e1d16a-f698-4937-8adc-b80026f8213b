import jwt from "jsonwebtoken";
import redisClient from "../services/redis.service.js";


export const authUser = async (req, res, next) => {
  try {
    let token;

    // Check for token in cookies
    if (req.cookies?.token) {
      token = req.cookies.token;
    }
    // Check for token in Authorization header
    else if (req.headers.authorization?.startsWith("Bearer ")) {
      token = req.headers.authorization.split(" ")[1];
    }
    // Check for token in query parameters (if needed)
    else if (req.query.Authorization?.startsWith("Bearer ")) {
      token = req.query.Authorization.split(" ")[1];
    }

    if (!token) {
      return res.status(401).send({ error: "Unauthorized User" });
    }

    // Check Redis blacklist (using mock client for development)
    const isBlacklisted = await redisClient.get(token);

    if (isBlacklisted) {
      res.cookie("token", "");
      return res.status(401).send({ error: "Unauthorized User" });
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
      next();
    } catch (jwtError) {
      if (jwtError instanceof jwt.TokenExpiredError) {
        res.cookie("token", ""); // Clear expired token
        return res.status(401).send({ error: "Token expired" });
      }
      throw jwtError; // Re-throw other JWT errors
    }
  } catch (error) {
    console.log(error);
    res.status(401).send({ error: "Unauthorized User" });
  }
};
